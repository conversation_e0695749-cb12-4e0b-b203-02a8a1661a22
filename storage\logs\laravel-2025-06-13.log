[2025-06-13 12:59:24] development.INFO: [AppController:banner] 1749819564801-req-a4a4e9f4-3f8c-4cb4-ad7c-7a8d5f6eab8e, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-13 13:01:34] development.INFO: [ProcessCampaignJob:handle] 1749819691670-campaign-340, processing: 340  
[2025-06-13 13:01:38] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 340  
[2025-06-13 13:01:38] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 340  
[2025-06-13 13:01:38] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 340  
[2025-06-13 13:01:38] development.INFO: [ProcessCampaignJob] Completed processing campaign: 340, processed: 3 contacts  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-340-contact-************, AccountId: 23  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Initialization completed in 14.18ms  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Validation completed in 0.01ms  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Edna"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"rKdGGEP0ysinN8Du","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"7732134297528795","name":"braun-corkery-and-lindgren_demo_1","mediaType":"NA","fields":[]}  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Template fetch and analysis completed in 582.56ms  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Edna"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"rKdGGEP0ysinN8Du","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Edna"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"rKdGGEP0ysinN8Du","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:01:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Parameter preparation completed in 0.46ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - WhatsApp API call completed in 544.8ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Response validation completed in 0ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Dialog save completed in 37.76ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Timeline Redis length: 1 for campaign: 340  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Campaign Redis length: 1 for campaign: 340  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Message Redis length: 1 for campaign: 340  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-340-contact-************ - Total execution time: 1186.33ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-340-contact-************ - Performance breakdown: Initialization: 14.18ms, Validation: 0.01ms, Template Fetch: 582.56ms, Parameter Preparation: 0.46ms, API Call: 544.8ms, Response Validation: 0ms, Dialog Save: 37.76ms, Total: 1186.33ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-340-contact-************ - Total execution time: 1186.59ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-340-contact-************, AccountId: 23  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Initialization completed in 1.85ms  
[2025-06-13 13:01:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Validation completed in 0.01ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Dewayne"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"awFT7YtEBkaHousV","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"7732134297528795","name":"braun-corkery-and-lindgren_demo_1","mediaType":"NA","fields":[]}  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Template fetch and analysis completed in 644.18ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Dewayne"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"awFT7YtEBkaHousV","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Dewayne"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"awFT7YtEBkaHousV","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Parameter preparation completed in 0.27ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - WhatsApp API call completed in 464.36ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Response validation completed in 0.01ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Dialog save completed in 27.71ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Timeline Redis length: 2 for campaign: 340  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Campaign Redis length: 2 for campaign: 340  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Message Redis length: 2 for campaign: 340  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-340-contact-************ - Total execution time: 1152.89ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-340-contact-************ - Performance breakdown: Initialization: 1.85ms, Validation: 0.01ms, Template Fetch: 644.18ms, Parameter Preparation: 0.27ms, API Call: 464.36ms, Response Validation: 0.01ms, Dialog Save: 27.71ms, Total: 1152.89ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-340-contact-************ - Total execution time: 1153.72ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-340-contact-************, AccountId: 23  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Initialization completed in 3.29ms  
[2025-06-13 13:01:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Validation completed in 0.01ms  
[2025-06-13 13:01:41] development.INFO: [AppController:banner] *************-req-babd5cd9-916e-4bce-9a3b-1f17904644e1, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Buster"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"kyDTca9wMt6Yp7NR","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"7732134297528795","name":"braun-corkery-and-lindgren_demo_1","mediaType":"NA","fields":[]}  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Template fetch and analysis completed in 608.18ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Buster"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"kyDTca9wMt6Yp7NR","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Buster"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"kyDTca9wMt6Yp7NR","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Parameter preparation completed in 0.26ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - WhatsApp API call completed in 604.08ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Response validation completed in 0ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Dialog save completed in 6.87ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Timeline Redis length: 3 for campaign: 340  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Campaign Redis length: 3 for campaign: 340  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-340-contact-************ - Message Redis length: 3 for campaign: 340  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-340-contact-************ - Total execution time: 1246.25ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-340-contact-************ - Performance breakdown: Initialization: 3.29ms, Validation: 0.01ms, Template Fetch: 608.18ms, Parameter Preparation: 0.26ms, API Call: 604.08ms, Response Validation: 0ms, Dialog Save: 6.87ms, Total: 1246.25ms  
[2025-06-13 13:01:41] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-340-contact-************ - Total execution time: 1247.28ms  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Found 1 timeline keys: vira_database_timeline_updates:340  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_timeline_updates:340, Clean key: timeline_updates:340, Length: 3  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Dispatching TimelineBatchJob for campaign 340 with 3 items  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Found 1 campaign keys: vira_database_campaign_updates:340  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_campaign_updates:340, Clean key: campaign_updates:340, Length: 3  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Dispatching CampaignBatchJob for campaign 340 with 3 items  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Found 1 message keys: vira_database_message_updates:340  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_message_updates:340, Clean key: message_updates:340, Length: 3  
[2025-06-13 13:02:05] development.INFO: [ProcessTimelineBatchesCommand] Dispatching MessageBatchJob for campaign 340 with 3 items  
[2025-06-13 13:02:05] development.INFO: [TimelineBatchJob:handle] 1749819725047-timeline-batch-340, processing batch for campaign: 340  
[2025-06-13 13:02:05] development.INFO: [TimelineBatchJob:handle] 1749819725047-timeline-batch-340, Retrieved {3} items from Redis key: timeline_updates:340  
[2025-06-13 13:02:06] development.INFO: [TimelineBatchJob:handle] 1749819725047-timeline-batch-340, Successfully processed batch of 3 items  
[2025-06-13 13:02:06] development.INFO: [TimelineBatchJob:handle] 1749819725047-timeline-batch-340, Completed in 646.75 ms  
[2025-06-13 13:02:06] development.INFO: [CampaignBatchJob:handle] 1749819725068-campaign-batch-340, processing batch for campaign: 340  
[2025-06-13 13:02:06] development.INFO: [CampaignBatchJob:handle] 1749819725068-campaign-batch-340, Retrieved {3} items from Redis key: campaign_updates:340  
[2025-06-13 13:02:06] development.INFO: [CampaignBatchJob:handle] 1749819725068-campaign-batch-340, Successfully processed batch of 3 campaign records  
[2025-06-13 13:02:06] development.INFO: [CampaignBatchJob:handle] 1749819725068-campaign-batch-340, Completed in 22.33 ms  
[2025-06-13 13:02:06] development.INFO: [MessageBatchJob:handle] 1749819725070-message-batch-340, processing batch for campaign: 340  
[2025-06-13 13:02:06] development.INFO: [MessageBatchJob:handle] 1749819725070-message-batch-340, Retrieved {3} items from Redis key: message_updates:340  
[2025-06-13 13:02:06] development.INFO: [MessageBatchJob:handle] 1749819725070-message-batch-340, Completed in 1.94 ms  
[2025-06-13 13:02:06] development.ERROR: App\Jobs\MessageBatchJob::failed(): Argument #1 ($e) must be of type Exception, Error given, called in D:\xampp\htdocs\vira\vendor\laravel\framework\src\Illuminate\Queue\CallQueuedHandler.php on line 295 {"exception":"[object] (TypeError(code: 0): App\\Jobs\\MessageBatchJob::failed(): Argument #1 ($e) must be of type Exception, Error given, called in D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php on line 295 at D:\\xampp\\htdocs\\vira\\app\\Jobs\\MessageBatchJob.php:77)
[stacktrace]
#0 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(295): App\\Jobs\\MessageBatchJob->failed(Object(Error))
#1 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(254): Illuminate\\Queue\\CallQueuedHandler->failed(Array, Object(Error), 'd55cc1b1-f10d-4...')
#2 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(219): Illuminate\\Queue\\Jobs\\Job->failed(Object(Error))
#3 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(604): Illuminate\\Queue\\Jobs\\Job->fail(Object(Error))
#4 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(550): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\RedisJob), Object(Error))
#5 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(473): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('redis', Object(Illuminate\\Queue\\Jobs\\RedisJob), 1, Object(Error))
#6 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(447): Illuminate\\Queue\\Worker->handleJobException('redis', Object(Illuminate\\Queue\\Jobs\\RedisJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Error))
#7 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('redis', Object(Illuminate\\Queue\\Jobs\\RedisJob), Object(Illuminate\\Queue\\WorkerOptions))
#8 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(177): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\RedisJob), 'redis', Object(Illuminate\\Queue\\WorkerOptions))
#9 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon('redis', 'lists', Object(Illuminate\\Queue\\WorkerOptions))
#10 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('redis', 'lists')
#11 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#12 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#17 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\xampp\\htdocs\\vira\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 {main}
"} 
[2025-06-13 13:04:37] development.INFO: [ProcessCampaignJob:handle] 1749819876544-campaign-341, processing: 341  
[2025-06-13 13:04:38] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 341  
[2025-06-13 13:04:38] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 341  
[2025-06-13 13:04:38] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 341  
[2025-06-13 13:04:38] development.INFO: [ProcessCampaignJob] Completed processing campaign: 341, processed: 3 contacts  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-341-contact-************, AccountId: 23  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Initialization completed in 1.35ms  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Validation completed in 0.01ms  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Fatima"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"7CcM3hY05fNn6vhS","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"3969131433671954","name":"brakus-orn_demo_1","mediaType":"NA","fields":[]}  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Template fetch and analysis completed in 575.07ms  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Fatima"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"7CcM3hY05fNn6vhS","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Fatima"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"7CcM3hY05fNn6vhS","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:04:38] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Parameter preparation completed in 0.22ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - WhatsApp API call completed in 525.1ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Response validation completed in 0.01ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Dialog save completed in 11.07ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Timeline Redis length: 1 for campaign: 341  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Campaign Redis length: 1 for campaign: 341  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Message Redis length: 1 for campaign: 341  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-341-contact-************ - Total execution time: 1122.68ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-341-contact-************ - Performance breakdown: Initialization: 1.35ms, Validation: 0.01ms, Template Fetch: 575.07ms, Parameter Preparation: 0.22ms, API Call: 525.1ms, Response Validation: 0.01ms, Dialog Save: 11.07ms, Total: 1122.68ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-341-contact-************ - Total execution time: 1123.14ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-341-contact-************, AccountId: 23  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Initialization completed in 1.83ms  
[2025-06-13 13:04:39] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Validation completed in 0.01ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Reinhold"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"oKQAOqZEkNCpo9iQ","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"3969131433671954","name":"brakus-orn_demo_1","mediaType":"NA","fields":[]}  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Template fetch and analysis completed in 563.8ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Reinhold"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"oKQAOqZEkNCpo9iQ","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Reinhold"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"oKQAOqZEkNCpo9iQ","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Parameter preparation completed in 0.23ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - WhatsApp API call completed in 490ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Response validation completed in 0.01ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Dialog save completed in 12.38ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Timeline Redis length: 2 for campaign: 341  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Campaign Redis length: 2 for campaign: 341  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Message Redis length: 2 for campaign: 341  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-341-contact-************ - Total execution time: 1076.91ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-341-contact-************ - Performance breakdown: Initialization: 1.83ms, Validation: 0.01ms, Template Fetch: 563.8ms, Parameter Preparation: 0.23ms, API Call: 490ms, Response Validation: 0.01ms, Dialog Save: 12.38ms, Total: 1076.91ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-341-contact-************ - Total execution time: 1077.25ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-341-contact-************, AccountId: 23  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Initialization completed in 1.79ms  
[2025-06-13 13:04:40] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Validation completed in 0.01ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Micheal"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"umgoSPZZ7PbHhhVK","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"3969131433671954","name":"brakus-orn_demo_1","mediaType":"NA","fields":[]}  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Template fetch and analysis completed in 454.18ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Micheal"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"umgoSPZZ7PbHhhVK","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Micheal"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"umgoSPZZ7PbHhhVK","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Parameter preparation completed in 0.16ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - WhatsApp API call completed in 519.94ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Response validation completed in 0ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Dialog save completed in 13.3ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Timeline Redis length: 3 for campaign: 341  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Campaign Redis length: 3 for campaign: 341  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] *************-campaign-341-contact-************ - Message Redis length: 3 for campaign: 341  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-341-contact-************ - Total execution time: 994.78ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-341-contact-************ - Performance breakdown: Initialization: 1.79ms, Validation: 0.01ms, Template Fetch: 454.18ms, Parameter Preparation: 0.16ms, API Call: 519.94ms, Response Validation: 0ms, Dialog Save: 13.3ms, Total: 994.78ms  
[2025-06-13 13:04:41] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-341-contact-************ - Total execution time: 994.98ms  
[2025-06-13 13:04:43] development.INFO: [AppController:banner] 1749819882993-req-2594adcc-24e0-497d-854b-564ec33a61a1, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Found 1 timeline keys: vira_database_timeline_updates:341  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_timeline_updates:341, Clean key: timeline_updates:341, Length: 3  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Dispatching TimelineBatchJob for campaign 341 with 3 items  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Found 1 campaign keys: vira_database_campaign_updates:341  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_campaign_updates:341, Clean key: campaign_updates:341, Length: 3  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Dispatching CampaignBatchJob for campaign 341 with 3 items  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Found 1 message keys: vira_database_message_updates:341  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_message_updates:341, Clean key: message_updates:341, Length: 3  
[2025-06-13 13:04:52] development.INFO: [ProcessTimelineBatchesCommand] Dispatching MessageBatchJob for campaign 341 with 3 items  
[2025-06-13 13:04:53] development.INFO: [TimelineBatchJob:handle] 1749819892771-timeline-batch-341, processing batch for campaign: 341  
[2025-06-13 13:04:53] development.INFO: [TimelineBatchJob:handle] 1749819892771-timeline-batch-341, Retrieved {3} items from Redis key: timeline_updates:341  
[2025-06-13 13:04:54] development.INFO: [TimelineBatchJob:handle] 1749819892771-timeline-batch-341, Successfully processed batch of 3 items  
[2025-06-13 13:04:54] development.INFO: [TimelineBatchJob:handle] 1749819892771-timeline-batch-341, Completed in 646.62 ms  
[2025-06-13 13:04:54] development.INFO: [CampaignBatchJob:handle] 1749819892787-campaign-batch-341, processing batch for campaign: 341  
[2025-06-13 13:04:54] development.INFO: [CampaignBatchJob:handle] 1749819892787-campaign-batch-341, Retrieved {3} items from Redis key: campaign_updates:341  
[2025-06-13 13:04:54] development.INFO: [CampaignBatchJob:handle] 1749819892787-campaign-batch-341, Successfully processed batch of 3 campaign records  
[2025-06-13 13:04:54] development.INFO: [CampaignBatchJob:handle] 1749819892787-campaign-batch-341, Completed in 9.73 ms  
[2025-06-13 13:04:54] development.INFO: [MessageBatchJob:handle] 1749819892790-message-batch-341, processing batch for campaign: 341  
[2025-06-13 13:04:54] development.INFO: [MessageBatchJob:handle] 1749819892790-message-batch-341, Retrieved {3} items from Redis key: message_updates:341  
[2025-06-13 13:04:54] development.INFO: [MessageBatchJob:handle] 1749819892790-message-batch-341, Completed in 1.49 ms  
[2025-06-13 13:04:54] development.ERROR: App\Jobs\MessageBatchJob::failed(): Argument #1 ($e) must be of type Exception, Error given, called in D:\xampp\htdocs\vira\vendor\laravel\framework\src\Illuminate\Queue\CallQueuedHandler.php on line 295 {"exception":"[object] (TypeError(code: 0): App\\Jobs\\MessageBatchJob::failed(): Argument #1 ($e) must be of type Exception, Error given, called in D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php on line 295 at D:\\xampp\\htdocs\\vira\\app\\Jobs\\MessageBatchJob.php:77)
[stacktrace]
#0 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(295): App\\Jobs\\MessageBatchJob->failed(Object(Error))
#1 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(254): Illuminate\\Queue\\CallQueuedHandler->failed(Array, Object(Error), '2214ba0a-2a9a-4...')
#2 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(219): Illuminate\\Queue\\Jobs\\Job->failed(Object(Error))
#3 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(604): Illuminate\\Queue\\Jobs\\Job->fail(Object(Error))
#4 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(550): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\RedisJob), Object(Error))
#5 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(473): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('redis', Object(Illuminate\\Queue\\Jobs\\RedisJob), 1, Object(Error))
#6 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(447): Illuminate\\Queue\\Worker->handleJobException('redis', Object(Illuminate\\Queue\\Jobs\\RedisJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Error))
#7 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('redis', Object(Illuminate\\Queue\\Jobs\\RedisJob), Object(Illuminate\\Queue\\WorkerOptions))
#8 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(177): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\RedisJob), 'redis', Object(Illuminate\\Queue\\WorkerOptions))
#9 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon('redis', 'lists', Object(Illuminate\\Queue\\WorkerOptions))
#10 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('redis', 'lists')
#11 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#12 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#17 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\xampp\\htdocs\\vira\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\xampp\\htdocs\\vira\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 {main}
"} 
