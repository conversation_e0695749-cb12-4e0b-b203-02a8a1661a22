<?php

namespace App\Jobs;

use Log;
use Exception;
use Throwable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Crypt;

class MessageBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $campaignId;
    protected $requestId;

    public function __construct($campaignId)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-message-batch-'.$campaignId;
    }

    public function handle(): void
    {
        $startTime = microtime(true);
        Log::info("[MessageBatchJob:handle] {$this->requestId}, processing batch for campaign: {$this->campaignId}");

        $cacheKey = "message_updates:{$this->campaignId}";
        $batchSize = 100;
        $batch = [];

        try {
            // Process up to 100 items from Redis
            for ($i = 0; $i < $batchSize; $i++) {
                $item = Redis::lpop($cacheKey);
                if (!$item) {
                    break;
                }
                $batch[] = json_decode($item, true);
            }

            Log::info("[MessageBatchJob:handle] {$this->requestId}, Retrieved {" . count($batch) . "} items from Redis key: {$cacheKey}");

            if (!empty($batch)) {
                try {
                    // Insert message data into database
                    // Note: The Message model already handles encryption via mutators,
                    // so we don't need to encrypt here to avoid double encryption
                    DB::table('messages')->insert($batch);
                    Log::info("[MessageBatchJob:handle] {$this->requestId}, Successfully processed batch of " . count($batch) . " message records");
                } catch (Exception $e) {
                    // If batch insert fails, push items back to Redis
                    Log::error("[MessageBatchJob:handle] {$this->requestId}, Batch insert failed: " . $e->getMessage());
                    foreach ($batch as $item) {
                        Redis::lpush($cacheKey, json_encode($item));
                    }
                    throw $e;
                }
            }

        } catch (Exception $e) {
            Log::error("[MessageBatchJob:handle] {$this->requestId}, Error: " . $e->getMessage());
            throw $e;
        } finally {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("[MessageBatchJob:handle] {$this->requestId}, Completed in {$duration} ms");
        }
    }

    public function failed(Throwable $e)
    {
        Log::error("[MessageBatchJob:failed] {$this->requestId}, Exception: " . $e->getMessage());
    }
}
