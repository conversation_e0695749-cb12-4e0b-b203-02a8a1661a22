<?php

namespace App\Console\Commands;

use Log;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Jobs\TimelineBatchJob;
use App\Jobs\CampaignBatchJob;
use App\Jobs\MessageBatchJob;

class ProcessTimelineBatchesCommand extends Command
{
    protected $signature = 'process:timeline-batches';
    protected $description = 'Process any remaining timeline, campaign, and message updates that haven\'t reached batch size';

    public function handle()
    {
        $this->info('Starting batch processing for timeline, campaign, and message updates...');

        try {
            // Process timeline updates
            $this->processTimelineBatches();

            // Process campaign updates
            $this->processCampaignBatches();

            // Process message updates
            $this->processMessageBatches();

            $this->info('All batch processing completed');
        } catch (\Exception $e) {
            Log::error("[ProcessTimelineBatchesCommand] Error: " . $e->getMessage());
            $this->error('Error processing batches: ' . $e->getMessage());
        }
    }

    private function processTimelineBatches()
    {
        $keys = Redis::keys('*timeline_updates:*');
        Log::info("[ProcessTimelineBatchesCommand] Found " . count($keys) . " timeline keys: " . implode(', ', $keys));

        foreach ($keys as $key) {
            // Extract campaign ID from key (handle prefixed keys)
            $campaignId = preg_replace('/.*timeline_updates:(\d+)/', '$1', $key);

            // Remove prefix from key for llen operation
            $cleanKey = preg_replace('/.*_database_/', '', $key);
            $length = Redis::llen($cleanKey);

            Log::info("[ProcessTimelineBatchesCommand] Key: {$key}, Clean key: {$cleanKey}, Length: {$length}");

            if ($length > 0) {
                $this->info("Found {$length} pending timeline updates for campaign {$campaignId}");
                Log::info("[ProcessTimelineBatchesCommand] Dispatching TimelineBatchJob for campaign {$campaignId} with {$length} items");
                dispatch(new TimelineBatchJob($campaignId))->onQueue('lists');
            }
        }
    }

    private function processCampaignBatches()
    {
        $keys = Redis::keys('*campaign_updates:*');
        Log::info("[ProcessTimelineBatchesCommand] Found " . count($keys) . " campaign keys: " . implode(', ', $keys));

        foreach ($keys as $key) {
            // Extract campaign ID from key (handle prefixed keys)
            $campaignId = preg_replace('/.*campaign_updates:(\d+)/', '$1', $key);

            // Remove prefix from key for llen operation
            $cleanKey = preg_replace('/.*_database_/', '', $key);
            $length = Redis::llen($cleanKey);

            Log::info("[ProcessTimelineBatchesCommand] Key: {$key}, Clean key: {$cleanKey}, Length: {$length}");

            if ($length > 0) {
                $this->info("Found {$length} pending campaign updates for campaign {$campaignId}");
                Log::info("[ProcessTimelineBatchesCommand] Dispatching CampaignBatchJob for campaign {$campaignId} with {$length} items");
                dispatch(new CampaignBatchJob($campaignId))->onQueue('lists');
            }
        }
    }

    private function processMessageBatches()
    {
        $keys = Redis::keys('*message_updates:*');
        Log::info("[ProcessTimelineBatchesCommand] Found " . count($keys) . " message keys: " . implode(', ', $keys));

        foreach ($keys as $key) {
            // Extract campaign ID from key (handle prefixed keys)
            $campaignId = preg_replace('/.*message_updates:(\d+)/', '$1', $key);

            // Remove prefix from key for llen operation
            $cleanKey = preg_replace('/.*_database_/', '', $key);
            $length = Redis::llen($cleanKey);

            Log::info("[ProcessTimelineBatchesCommand] Key: {$key}, Clean key: {$cleanKey}, Length: {$length}");

            if ($length > 0) {
                $this->info("Found {$length} pending message updates for campaign {$campaignId}");
                Log::info("[ProcessTimelineBatchesCommand] Dispatching MessageBatchJob for campaign {$campaignId} with {$length} items");
                dispatch(new MessageBatchJob($campaignId))->onQueue('lists');
            }
        }
    }
}